# Makefile for Q&A Agent Project (using uv)
.PHONY: help install run test clean docker-build docker-run docker-stop format lint sync

# Default target
help:
	@echo "Available commands:"
	@echo "  uv-check     - Check if uv is installed"
	@echo "  install      - Install dependencies using uv"
	@echo "  sync         - Sync dependencies with uv"
	@echo "  add          - Add a new dependency (usage: make add PACKAGE=name)"
	@echo "  add-dev      - Add a development dependency (usage: make add-dev PACKAGE=name)"
	@echo "  remove       - Remove a dependency (usage: make remove PACKAGE=name)"
	@echo "  run          - Run the application locally"
	@echo "  test         - Run tests"
	@echo "  clean        - Clean up temporary files and virtual environment"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run with docker-compose"
	@echo "  docker-stop  - Stop docker-compose services"
	@echo "  format       - Format code with black"
	@echo "  lint         - Run linting checks"
	@echo "  requirements - Generate requirements.txt"
	@echo "  dev-install  - Install development dependencies"
	@echo "  quick-start  - Quick setup for new users"

# uv configuration
UV = uv
PYTHON = python3

# Check if uv is installed
uv-check:
	@command -v $(UV) >/dev/null 2>&1 || { echo "uv is not installed. Install it with: curl -LsSf https://astral.sh/uv/install.sh | sh"; exit 1; }
	@echo "✅ uv is installed: $$($(UV) --version)"

# Install dependencies using uv
install: uv-check
	@echo "Installing dependencies with uv..."
	$(UV) pip install -r requirements.txt
	@echo "Setup complete! Dependencies installed with uv."
	@echo "To activate the virtual environment: source .venv/bin/activate"

# Sync dependencies (useful for updates)
sync: uv-check
	@echo "Syncing dependencies with uv..."
	$(UV) pip install -r requirements.txt

# Run the application locally
run:
	@echo "Starting Q&A Agent..."
	$(UV) run --with-requirements requirements.txt python main.py

# Run tests (add test files as needed)
test:
	@echo "Running tests..."
	$(UV) run --with-requirements requirements.txt pytest tests/ -v || echo "No tests found. Add tests in tests/ directory."

# Clean up temporary files and virtual environment
clean:
	@echo "Cleaning up..."
	rm -rf .venv
	rm -rf __pycache__
	rm -rf *.pyc
	rm -rf .pytest_cache
	rm -rf uv.lock
	find . -type d -name "__pycache__" -delete
	find . -type f -name "*.pyc" -delete

# Docker commands
docker-build:
	@echo "Building Docker image..."
	docker build -t qa-agent .

docker-run:
	@echo "Starting services with docker-compose..."
	docker-compose up -d

docker-stop:
	@echo "Stopping docker-compose services..."
	docker-compose down

docker-logs:
	@echo "Showing docker-compose logs..."
	docker-compose logs -f

# Code formatting and linting
format:
	@echo "Formatting code with black..."
	$(UV) run --with black black . || echo "Install black: uv pip install black"

lint:
	@echo "Running linting checks..."
	$(UV) run --with flake8 flake8 . || echo "Install flake8: uv pip install flake8"

# Generate requirements.txt from current environment
requirements:
	@echo "Generating requirements.txt..."
	$(UV) pip freeze > requirements.txt

# Development helpers
dev-install:
	@echo "Installing development dependencies..."
	$(UV) pip install black flake8 pytest pytest-asyncio

# Quick start for new users
quick-start: install
	@echo "Quick start complete!"
	@echo "1. Copy .env.example to .env and add your API keys"
	@echo "2. Run 'make run' to start the application"
	@echo "3. Visit http://localhost:8000/docs for API documentation"

# Add a new dependency
add:
	@echo "Usage: make add PACKAGE=package_name"
	@echo "Example: make add PACKAGE=requests"
ifdef PACKAGE
	$(UV) pip install $(PACKAGE)
	@echo "Don't forget to update requirements.txt with: make requirements"
else
	@echo "Please specify PACKAGE=package_name"
endif

# Add a development dependency
add-dev:
	@echo "Usage: make add-dev PACKAGE=package_name"
	@echo "Example: make add-dev PACKAGE=pytest"
ifdef PACKAGE
	$(UV) pip install $(PACKAGE)
	@echo "Don't forget to update requirements.txt with: make requirements"
else
	@echo "Please specify PACKAGE=package_name"
endif

# Remove a dependency
remove:
	@echo "Usage: make remove PACKAGE=package_name"
	@echo "Example: make remove PACKAGE=requests"
ifdef PACKAGE
	$(UV) pip uninstall $(PACKAGE)
	@echo "Don't forget to update requirements.txt with: make requirements"
else
	@echo "Please specify PACKAGE=package_name"
endif
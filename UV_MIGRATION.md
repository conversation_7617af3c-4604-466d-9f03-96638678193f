# Migration Guide: From pip/venv to uv

This guide helps you migrate from the traditional Python virtual environment setup to using `uv` for faster dependency management.

## What is uv?

`uv` is an extremely fast Python package installer and resolver, written in Rust. It's designed to be a drop-in replacement for pip and pip-tools, with significantly better performance.

## Installation

### Install uv

```bash
# On macOS and Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# On Windows
powershell -c "irm https://astral.sh/uv/install.sh | iex"

# Alternative: using pip
pip install uv
```

### Verify installation

```bash
uv --version
```

## Migration Steps

### 1. Clean up old virtual environment (optional)

```bash
# Remove old virtual environment
make clean
# or manually:
rm -rf qagent_venv
```

### 2. Install dependencies with uv

```bash
# Check if uv is installed
make uv-check

# Install all dependencies
make install
```

This will:
- Create a new virtual environment in `.venv/`
- Install all dependencies from `requirements.txt`

### 3. Activate the virtual environment

```bash
# Activate the virtual environment
source .venv/bin/activate

# Or on Windows
.venv\Scripts\activate
```

## New Makefile Commands

### Basic Commands
- `make uv-check` - Check if uv is installed
- `make install` - Install all dependencies
- `make sync` - Sync dependencies (useful after updating requirements.txt)
- `make run` - Run the application
- `make test` - Run tests

### Dependency Management
- `make add PACKAGE=requests` - Add a new dependency
- `make add-dev PACKAGE=pytest` - Add a development dependency
- `make remove PACKAGE=requests` - Remove a dependency

### Development
- `make format` - Format code with black
- `make lint` - Run linting checks
- `make dev-install` - Install development dependencies

## Key Differences

### Old way (pip/venv)
```bash
python -m venv qagent_venv
source qagent_venv/bin/activate
pip install -r requirements.txt
python main.py
```

### New way (uv)
```bash
make install
make run
# or directly:
uv run python main.py
```

## Benefits of uv

1. **Speed**: 10-100x faster than pip
2. **Better dependency resolution**: More reliable conflict resolution
3. **Modern tooling**: Fast package installation and management
4. **No activation needed**: `uv run` works without activating the virtual environment
5. **Easy dependency management**: Simple commands for adding/removing packages

## File Changes

### New files created:
- `UV_MIGRATION.md` - This migration guide

### Modified files:
- `Makefile` - Updated to use uv commands

### Files you can keep:
- `requirements.txt` - Still used for dependency management
- `.env` and `.env.example` - No changes needed
- All your Python source files - No changes needed

## Troubleshooting

### uv not found
```bash
# Install uv
curl -LsSf https://astral.sh/uv/install.sh | sh
# Restart your terminal or source your shell profile
```

### Dependencies not found
```bash
# Sync dependencies
make sync
# or
uv sync
```

### Virtual environment issues
```bash
# Clean and reinstall
make clean
make install
```

## Docker Usage

The Docker setup remains the same - no changes needed to docker-compose.yml or Dockerfile.

## Rollback (if needed)

If you need to go back to the old setup:

1. Remove uv-related files:
   ```bash
   rm -rf .venv
   ```

2. Restore old virtual environment:
   ```bash
   python -m venv qagent_venv
   source qagent_venv/bin/activate
   pip install -r requirements.txt
   ```

3. Use the old Makefile commands (you'd need to restore the old Makefile)

## Next Steps

1. Try the new commands: `make install`, `make run`
2. Add new dependencies with: `make add PACKAGE=package_name`
3. Enjoy the faster dependency resolution!

For more information about uv, visit: https://github.com/astral-sh/uv

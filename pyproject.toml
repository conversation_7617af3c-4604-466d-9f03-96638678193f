[project]
name = "qagent"
version = "1.0.0"
description = "A Q&A agent that searches specific domains using <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
keywords = ["qa", "agent", "langchain", "search", "ai"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]

dependencies = [
    # Core dependencies
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-dotenv>=1.0.1",
    
    # <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>
    "langchain>=0.2.0",
    "langchain-google-genai>=1.0.0",
    "google-generativeai>=0.7.0",
    
    # Search tool
    "tavily-python>=0.3.0",
    
    # Web scraping dependencies
    "playwright>=1.40.0",
    "beautifulsoup4>=4.12.0",
    "langchain-community>=0.2.0",
    
    # Data processing
    "pandas>=2.2.0",
    
    # HTTP client
    "requests>=2.32.0",
    "aiohttp>=3.9.1",
]

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "flake8>=6.0.0",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
]

[project.urls]
Homepage = "https://github.com/javiramos1/qagent"
Repository = "https://github.com/javiramos1/qagent.git"
Issues = "https://github.com/javiramos1/qagent/issues"

[project.scripts]
qagent = "main:app"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [".git", "__pycache__", ".venv", "build", "dist"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"

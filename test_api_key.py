#!/usr/bin/env python3
"""
Test script to validate Google API key for Gemini
"""

import os
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI

def test_google_api_key():
    """Test if Google API key is working"""
    load_dotenv()
    
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ GOOGLE_API_KEY not found in environment variables")
        return False
    
    print(f"✅ Found API key: {api_key[:10]}...")
    
    try:
        # Initialize the model
        llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            google_api_key=api_key,
            temperature=0.1,
            max_tokens=100,
            timeout=30,
        )
        
        # Test with a simple query
        print("🧪 Testing API key with simple query...")
        response = llm.invoke("Say 'Hello, API key is working!'")
        print(f"✅ API Response: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ API Key test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔑 Testing Google API Key for Gemini...")
    success = test_google_api_key()
    
    if success:
        print("\n✅ Google API key is working correctly!")
    else:
        print("\n❌ Google API key test failed. Please check:")
        print("1. API key is correct and not expired")
        print("2. Gemini API is enabled in Google Cloud Console")
        print("3. API key has proper permissions")
        print("4. You haven't exceeded rate limits")
        print("\nTo get a new API key:")
        print("- Visit: https://ai.google.dev")
        print("- Sign in and create/get an API key")
        print("- Make sure Gemini API is enabled")
